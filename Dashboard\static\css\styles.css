/* Main styles */
body {
    font-family: 'Gotham', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #0D0C33;
    color: #e0e0e0;
}

/* Navbar and logo */
.proterra-logo {
    height: 30px;
    width: auto;
}

.hexmes-logo {
    height: 30px;
    width: auto;
}

.hexmes-logo-lg {
    height: 40px;
    width: auto;
}

.navbar,
.footer {
    background-color: #1A1850;
}

.navbar-brand {
    font-weight: 500;
}

/* Card styles */
.card {
    background-color: #1A1850;
    border-color: #333;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
}

.card-header {
    background-color: #0D0C33;
    border-bottom-color: #333;
}

.card-footer {
    background-color: #0D0C33;
    border-top-color: #333;
}

/* Status badges */
.status-badge {
    font-size: 0.8rem;
    padding: 0.3rem 0.6rem;
}

/* Bridge cards */
.bridge-card {
    transition: all 0.2s ease;
}

.bridge-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.3);
}

/* Status colors */
.status-online {
    color: #82EB6A;
}

.status-offline {
    color: #AE3D58;
}

.status-unknown {
    color: #BDD0D7;
}

/* Form controls */
.form-control, .form-select {
    background-color: #2d2d2d;
    border-color: #444;
    color: #e0e0e0;
}

.form-control:focus, .form-select:focus {
    background-color: #333;
    border-color: #18DFD5;
    color: #fff;
}

/* Table styles */
.table {
    color: #e0e0e0;
}

.table-hover tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* Device list */
.connected-devices {
    max-height: 250px;
    overflow-y: auto;
}

/* Footer */
.footer {
    margin-top: 3rem;
    border-top: 1px solid #333;
}

/* Button hover effects */
.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background-color: #18DFD5;
    border-color: #18DFD5;
    color: #0D0C33;
}

.btn-primary:hover {
    background-color: #14b5ae;
    border-color: #14b5ae;
    color: #0D0C33;
}

/* Modal styling */
.modal-content {
    background-color: #2d2d2d;
    border-color: #444;
}

.modal-header, .modal-footer {
    border-color: #444;
}

/* Alert styling */
.alert-info {
    background-color: #18DFD5;
    border-color: #023F38;
    color: #0D0C33;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .proterra-logo {
        height: 24px;
    }
}

/* Highlight devices in testing mode */
.table-warning {
    background-color: #fff3cd;
}

/* Add a testing indicator */
tr.table-warning td:first-child::before {
    content: "🧪 ";
}

/* Device card styles */
.device-card {
    /* ...existing styles... */
}

.device-card.testing {
    border: 2px solid #ffd700;
}

.device-status {
    padding: 4px 8px;
    border-radius: 4px;
}

.status-active {
    background-color: #82EB6A;
    color: #0D0C33;
}

.status-inactive {
    background-color: #AE3D58;
    color: white;
}

.status-unknown {
    background-color: #BDD0D7;
    color: #0D0C33;
}

/* Port status styles */
.port-status {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

.port-status.text-success {
    background-color: rgba(40, 167, 69, 0.1);
}

.port-status.text-danger {
    background-color: rgba(220, 53, 69, 0.1);
}

/* Ultrasonic welder data container */
.welder-results {
    max-height: 150px;
    overflow-y: auto;
    background-color: #2d2d2d;
    border: 1px solid #444;
}

/* Icon spin animation for status checks */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
.spin {
    animation: spin 1s linear infinite;
}
